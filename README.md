# Gmail Account Creator

Automated Gmail account creation using uiautomator2 for Android devices.

## Features

- 🎲 **Random Name Generation**: Generates realistic first and last names
- 🔐 **Secure Password Generation**: Creates strong 12-character passwords
- 📅 **Birthday Generation**: Random birthdays for users aged 18-65
- 📱 **Smart UI Detection**: Works across different Android versions
- 💾 **Auto-save**: Saves account details and screenshots
- 🛡️ **Error Handling**: Takes screenshots on errors for debugging

## Requirements

```bash
pip install uiautomator2
```

## Setup

1. Enable USB Debugging on your Android device
2. Connect device to computer via USB
3. Install uiautomator2 on device:
   ```bash
   python -m uiautomator2 init
   ```

## Usage

```bash
python gmail_account_creator.py
```

## Output Files

- `gmail_account_YYYYMMDD_HHMMSS.png` - Final screenshot
- `account_info_YYYYMMDD_HHMMSS.json` - Account details
- `error_YYYYMMDD_HHMMSS.png` - Error screenshot (if failed)

## Account Info Structure

```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON>", 
  "password": "SecurePass123!",
  "birthday": "05/15/1995",
  "created_at": "2024-01-01T12:00:00"
}
```

## Notes

- Ensure device is unlocked during execution
- Process may take 2-3 minutes depending on device speed
- Script handles various Android UI layouts automatically
- Generated passwords meet Google's security requirements

## Troubleshooting

- If script fails, check the error screenshot
- Ensure device has internet connection
- Try running with device in portrait mode
- Clear Google app cache if issues persist
