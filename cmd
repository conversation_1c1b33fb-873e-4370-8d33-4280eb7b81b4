emulator -avd Android12_PlayStore -no-audio -gpu host -no-snapshot -skin 720x1280 -window-size 360x640   -dns-server *******,*******

emulator -avd Android12_PlayStore -skindir C:\Android\Sdk\skins -skin pixel_8_pro -no-audio -gpu host -no-snapshot -dns-server *******,******* -http-proxy http://e98f5489956302bda457__cr.gb:<EMAIL>:823

emulator -avd Android12_PlayStore -skindir C:\Android\Sdk\skins -skin pixel_8_pro -no-audio -gpu host -no-snapshot -dns-server *******,******* -http-proxy http://e98f546302bda457__cr.gb:<EMAIL>:823


#create android emulator:
avdmanager create avd -n Android13_3 -k "system-images;android-33;google_apis_playstore;x86_64" 


emulator -avd Android13_2 -skindir C:\Android\Sdk\skins -skin pixel_8_pro -no-audio -gpu host -no-snapshot -dns-server *******,******* -http-proxy http://e98f546302bda457__cr.gb:<EMAIL>:823


emulator -avd Android13_3 -skindir C:\Android\Sdk\skins -skin pixel_8_pro -no-audio -gpu host -no-snapshot -dns-server *******,*******
