import uiautomator2 as u2
import random
import string
import json
import time
from datetime import datetime, timedelta

class GmailAccountCreator:
    def __init__(self):
        self.device = u2.connect()
        self.account_info = {}
        
    def generate_name(self):
        """Generate random first and last names"""
        first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        last_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        return first_name, last_name
    
    def generate_password(self, length=12):
        """Generate a secure password"""
        chars = string.ascii_letters + string.digits + "!@#$%"
        password = ''.join(random.choice(chars) for _ in range(length))
        # Ensure it has at least one uppercase, lowercase, digit, and special char
        password = password[:8] + random.choice(string.ascii_uppercase) + random.choice(string.digits) + random.choice("!@#") + password[11:]
        return password
    
    def generate_birthday(self):
        """Generate a random birthday (18-65 years old)"""
        today = datetime.now()
        start_date = today - timedelta(days=65*365)
        end_date = today - timedelta(days=18*365)
        
        random_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
        return random_date.strftime("%m/%d/%Y")
    
    def wait_and_click(self, selector, timeout=10):
        """Wait for element and click it"""
        if self.device(**selector).wait(timeout=timeout):
            self.device(**selector).click()
            time.sleep(1)
            return True
        return False
    
    def wait_and_type(self, selector, text, timeout=10):
        """Wait for element and type text"""
        if self.device(**selector).wait(timeout=timeout):
            self.device(**selector).set_text(text)
            time.sleep(1)
            return True
        return False

    def debug_screen_elements(self):
        """Debug function to print all clickable elements with text"""
        print("=== DEBUG: Current screen elements ===")
        try:
            # Check specific elements that might contain "Create"
            all_elements = self.device(textContains="Create")
            for i in range(all_elements.count):
                elem = all_elements[i]
                if elem.exists:
                    info = elem.info
                    print(f"Element {i}: text='{info.get('text', '')}', clickable={info.get('clickable', False)}")
                    print(f"  className='{info.get('className', '')}', bounds={info.get('bounds', {})}")
        except Exception as e:
            print(f"Debug error: {e}")
        print("=== END DEBUG ===\n")
    
    def create_account(self):
        """Main function to create Gmail account"""
        print("Starting Gmail account creation...")
        
        # Generate account info
        first_name, last_name = self.generate_name()
        password = self.generate_password()
        birthday = self.generate_birthday()
        
        self.account_info = {
            'first_name': first_name,
            'last_name': last_name,
            'password': password,
            'birthday': birthday,
            'created_at': datetime.now().isoformat()
        }
        
        print(f"Generated info: {first_name} {last_name}, Birthday: {birthday}")
        
        try:
            # Step 1: Open Settings
            print("Opening Settings...")
            self.device.app_start("com.android.settings")
            time.sleep(3)
            
            # Step 2: Navigate to Accounts
            print("Looking for Accounts...")
            accounts_selectors = [
                {"text": "Passwords & accounts"},
                {"textContains": "Passwords"},
                {"text": "Passwords & accounts"},
                {"textContains": "& accounts"}
            ]
            
            found_accounts = False
            for selector in accounts_selectors:
                if self.wait_and_click(selector, timeout=5):
                    found_accounts = True
                    break
            
            if not found_accounts:
                print("Scrolling to find Accounts...")
                self.device.swipe(500, 800, 500, 300, 0.5)
                time.sleep(2)
                for selector in accounts_selectors:
                    if self.wait_and_click(selector, timeout=3):
                        found_accounts = True
                        break
            
            if not found_accounts:
                raise Exception("Could not find Accounts section")
            
            time.sleep(2)
            
            # Step 3: Add Google Account
            print("Adding Google account...")
            add_selectors = [
                {"text": "Add account"},
                {"textContains": "Add"},
                {"resourceId": "android:id/button1"}
            ]
            
            for selector in add_selectors:
                if self.wait_and_click(selector, timeout=5):
                    break
            
            time.sleep(2)
            
            # Step 4: Select Google (in the account list, not the menu)
            print("Selecting Google from account options...")

            # First, let's debug what Google elements we have
            print("=== DEBUG: All Google elements ===")
            all_google_elements = self.device(textContains="Google")
            if all_google_elements.exists:
                count = getattr(all_google_elements, 'count', 1)
                print(f"Found {count} Google elements")
                for i in range(count):
                    try:
                        elem = all_google_elements[i] if count > 1 else all_google_elements
                        info = elem.info
                        bounds = info.get('bounds', {})
                        print(f"Element {i}: text='{info.get('text', '')}', bounds={bounds}")
                        print(f"  className='{info.get('className', '')}', clickable={info.get('clickable', False)}")
                    except:
                        pass
            print("=== END DEBUG ===")

            google_found = False

            # Strategy 1: Look for Google that's NOT the first one (avoid menu item)
            if all_google_elements.exists:
                count = getattr(all_google_elements, 'count', 1)
                if count > 1:
                    # Skip the first Google (likely the menu item) and click the second one
                    print("Multiple Google elements found, clicking the second one...")
                    try:
                        second_google = all_google_elements[1]  # Index 1 = second element
                        second_google.click()
                        google_found = True
                        print("Successfully clicked second Google element")
                    except Exception as e:
                        print(f"Failed to click second Google: {e}")

                # If only one Google or second click failed, try coordinate filtering
                if not google_found:
                    print("Trying coordinate-based selection...")
                    for i in range(count):
                        try:
                            elem = all_google_elements[i] if count > 1 else all_google_elements
                            bounds = elem.info.get('bounds', {})
                            left_x = bounds.get('left', 0)
                            print(f"Google element {i} left position: {left_x}")

                            # Click on Google elements that are more to the right (account list area)
                            if left_x > 300:  # Increased threshold
                                print(f"Clicking Google element {i} at position {left_x}")
                                elem.click()
                                google_found = True
                                break
                        except Exception as e:
                            print(f"Error checking element {i}: {e}")

            time.sleep(2)

            # Strategy 2: Try clicking in the account list area directly
            if not google_found:
                print("Trying to click in account list area...")
                # Click in the general area where Google should be in the account list
                # Based on your screenshot, the account list is on the right side
                screen_width = self.device.window_size()[0]
                # Click in the right half of the screen where "Google" with logo should be
                google_area_x = 1500 #int(screen_width * 0.75)  # 75% from left
                google_area_y = 500  # Approximate Y position for Google in account list

                print(f"Clicking in account list area at ({google_area_x}, {google_area_y})")
                self.device.click(google_area_x, google_area_y)
                google_found = True
                time.sleep(2)

                # Verify we're in the right place by looking for account creation options
                if not (self.device(textContains="Create").exists or
                       self.device(textContains="Sign in").exists):
                    print("Doesn't seem to be in Google account screen, trying alternative...")
                    google_found = False

            # Strategy 3: Manual coordinate click based on your screenshot
            if not google_found:
                print("Using manual coordinates for Google selection...")
                # Based on your screenshot, Google appears to be around these coordinates
                self.device.click(400, 400)  # Adjust these coordinates based on your screen
                google_found = True

            if not google_found:
                # Take debug screenshot
                debug_screenshot = f"debug_google_selection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                self.device.screenshot(debug_screenshot)
                print(f"Debug screenshot saved: {debug_screenshot}")
                raise Exception("Could not find Google option in account list")

            time.sleep(3)
            
            # Step 5: Create account
            print("Creating new account...")

            # Debug: Show what's on screen
            self.debug_screen_elements()

            create_selectors = [
                {"text": "Create account"},
                {"textContains": "Create account"},
                {"textMatches": ".*Create account.*"},
                {"className": "android.widget.TextView", "textContains": "Create"},
                {"clickable": True, "textContains": "Create"},
                {"text": "For personal use"}
            ]

            account_created = False
            for i, selector in enumerate(create_selectors):
                print(f"Trying selector {i+1}: {selector}")
                if self.wait_and_click(selector, timeout=3):
                    print(f"Success with selector {i+1}")
                    account_created = True
                    break

            if not account_created:
                # Try clicking by coordinates if text selectors fail
                print("Trying coordinate click for Create account...")
                # Look for any element containing "Create"
                create_elements = self.device(textContains="Create")
                if create_elements.exists:
                    for i in range(create_elements.count):
                        elem = create_elements[i]
                        if elem.exists:
                            print(f"Clicking on element {i} with text: '{elem.info.get('text', '')}'")
                            elem.click()
                            account_created = True
                            break

            if not account_created:
                # Last resort: try clicking on any text that looks like a link
                print("Last resort: looking for any account creation links...")
                link_selectors = [
                    {"textContains": "account"},
                    {"textContains": "Account"},
                    {"textContains": "sign up"},
                    {"textContains": "Sign up"}
                ]

                for selector in link_selectors:
                    if self.wait_and_click(selector, timeout=2):
                        account_created = True
                        break

            if not account_created:
                # Take screenshot for debugging
                debug_screenshot = f"debug_create_account_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                self.device.screenshot(debug_screenshot)
                print(f"Debug screenshot saved: {debug_screenshot}")
                raise Exception("Could not find or click Create account option")
            
            time.sleep(2)
            
            # If "For personal use" appears, click it
            self.wait_and_click({"text": "For my personal use"}, timeout=3)
            time.sleep(2)
            
            # Step 6: Fill in first name
            print(f"Entering first name: {first_name}")
            first_name_selectors = [
                {"text": "first name"},
                {"textContains": "First"},
                {"resourceId": "firstName"}
            ]
            
            for selector in first_name_selectors:
                if self.wait_and_type(selector, first_name, timeout=1):
                    break
            
            # Step 7: Fill in last name
            print(f"Entering last name: {last_name}")
            last_name_selectors = [
                {"text": "last name"},
                {"textContains": "Last"},
                {"resourceId": "lastName"}
            ]
            
            for selector in last_name_selectors:
                if self.wait_and_type(selector, last_name, timeout=1):
                    break
            
            # Step 8: Click Next
            print("Clicking Next...")
            self.wait_and_click({"text": "NEXT"}, timeout=1)
            time.sleep(3)
            
            # Step 9: Fill birthday and gender (if required)
            print(f"Entering birthday: {birthday}")
            if self.device(textContains="birthday").exists or self.device(textContains="Birthday").exists:
                # Handle birthday input
                month, day, year = birthday.split('/')

                # Handle Month dropdown selector
                print(f"Selecting month: {month}")
                month_selectors = [
                    {"text": "Month"},
                    {"textContains": "Month"},
                    {"resourceId": "month"}
                ]

                month_clicked = False
                for selector in month_selectors:
                    if self.wait_and_click(selector, timeout=3):
                        month_clicked = True
                        break

                if month_clicked:
                    time.sleep(1)
                    # Select the month from dropdown
                    month_names = ["January", "February", "March", "April", "May", "June",
                                 "July", "August", "September", "October", "November", "December"]
                    month_name = month_names[int(month) - 1]

                    if self.device(text=month_name).exists:
                        self.device(text=month_name).click()
                    else:
                        # Try clicking by month number if name doesn't work
                        self.device(text=month).click()
                    time.sleep(1)

                # Handle Day input field
                print(f"Entering day: {day}")
                day_selectors = [
                    {"text": "Day"},
                    {"textContains": "Day"},
                    {"resourceId": "day"}
                ]

                for selector in day_selectors:
                    if self.wait_and_type(selector, day, timeout=3):
                        break

                # Handle Year input field
                print(f"Entering year: {year}")
                year_selectors = [
                    {"text": "Year"},
                    {"textContains": "Year"},
                    {"resourceId": "year"}
                ]

                for selector in year_selectors:
                    if self.wait_and_type(selector, year, timeout=3):
                        break

                # Handle Gender dropdown selector
                print("Selecting gender...")
                gender_options = ["Female", "Male", "Rather not say", "Custom"]
                selected_gender = random.choice(gender_options[:3])  # Exclude Custom for simplicity

                gender_selectors = [
                    {"text": "Gender"},
                    {"textContains": "Gender"},
                    {"resourceId": "gender"}
                ]

                gender_clicked = False
                for selector in gender_selectors:
                    if self.wait_and_click(selector, timeout=3):
                        gender_clicked = True
                        break

                if gender_clicked:
                    time.sleep(1)
                    print(f"Selecting gender: {selected_gender}")
                    # Select the gender from dropdown
                    if self.device(text=selected_gender).exists:
                        self.device(text=selected_gender).click()
                        print(f"Successfully selected: {selected_gender}")
                    else:
                        # Fallback to "Rather not say" if specific gender not found
                        if self.device(text="Rather not say").exists:
                            self.device(text="Rather not say").click()
                            selected_gender = "Rather not say"
                            print("Fallback: Selected 'Rather not say'")

                    # Save gender to account info
                    self.account_info['gender'] = selected_gender
                    time.sleep(1)

                time.sleep(2)
                self.wait_and_click({"text": "NEXT"}, timeout=5)
                time.sleep(3)
            
            # Step 10: Handle username selection
            print("Handling username selection...")
            time.sleep(5)  # Wait for username suggestions to load
            
            # Try to select a suggested username or create custom one
            if self.device(textContains="@gmail.com").exists:
                # Click on first suggested username
                self.device(textContains="@gmail.com").click()
                time.sleep(2)
            
            self.wait_and_click({"text": "NEXT"}, timeout=5)
            time.sleep(3)
            
            # Step 11: Set password
            print("Setting password...")
            password_selectors = [
                {"text": "Password"},
                {"textContains": "password"},
                {"resourceId": "passwd"}
            ]
            
            for selector in password_selectors:
                if self.wait_and_type(selector, password, timeout=5):
                    break
            
            # Confirm password
            confirm_selectors = [
                {"text": "Confirm"},
                {"textContains": "Confirm"},
                {"resourceId": "confirm-passwd"}
            ]
            
            for selector in confirm_selectors:
                if self.wait_and_type(selector, password, timeout=5):
                    break
            
            self.wait_and_click({"text": "NEXT"}, timeout=5)
            time.sleep(3)
            
            # Step 12: Accept terms and conditions
            print("Accepting terms...")
            accept_selectors = [
                {"text": "I agree"},
                {"text": "Accept"},
                {"text": "Agree"},
                {"textContains": "agree"}
            ]
            
            for selector in accept_selectors:
                if self.wait_and_click(selector, timeout=5):
                    break
            
            time.sleep(5)
            
            # Step 13: Final screenshot and save info
            print("Taking final screenshot...")
            screenshot_path = f"gmail_account_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.device.screenshot(screenshot_path)
            
            # Save account info
            info_file = f"account_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(info_file, 'w') as f:
                json.dump(self.account_info, f, indent=2)
            
            print(f"Account creation completed!")
            print(f"Screenshot saved: {screenshot_path}")
            print(f"Account info saved: {info_file}")
            print(f"Account details: {self.account_info}")
            
            return True
            
        except Exception as e:
            print(f"Error during account creation: {e}")
            # Take error screenshot
            error_screenshot = f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.device.screenshot(error_screenshot)
            print(f"Error screenshot saved: {error_screenshot}")
            return False

if __name__ == "__main__":
    print("=== Gmail Account Creator ===")
    print("Make sure your Android device is connected and unlocked")
    input("Press Enter to start...")

    creator = GmailAccountCreator()
    success = creator.create_account()

    if success:
        print("\n✅ Account creation completed successfully!")
    else:
        print("\n❌ Account creation failed. Check error screenshot.")
