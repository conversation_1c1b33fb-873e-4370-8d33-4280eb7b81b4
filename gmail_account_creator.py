import uiautomator2 as u2
import random
import string
import json
import time
from datetime import datetime, timedelta

class GmailAccountCreator:
    def __init__(self):
        self.device = u2.connect()
        self.account_info = {}
        
    def generate_name(self):
        """Generate random first and last names"""
        first_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        last_names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        return first_name, last_name
    
    def generate_password(self, length=12):
        """Generate a secure password"""
        chars = string.ascii_letters + string.digits + "!@#$%"
        password = ''.join(random.choice(chars) for _ in range(length))
        # Ensure it has at least one uppercase, lowercase, digit, and special char
        password = password[:8] + random.choice(string.ascii_uppercase) + random.choice(string.digits) + random.choice("!@#") + password[11:]
        return password
    
    def generate_birthday(self):
        """Generate a random birthday (18-65 years old)"""
        today = datetime.now()
        start_date = today - timedelta(days=65*365)
        end_date = today - timedelta(days=18*365)
        
        random_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
        return random_date.strftime("%m/%d/%Y")
    
    def wait_and_click(self, selector, timeout=10):
        """Wait for element and click it"""
        if self.device(**selector).wait(timeout=timeout):
            self.device(**selector).click()
            time.sleep(1)
            return True
        return False
    
    def wait_and_type(self, selector, text, timeout=10):
        """Wait for element and type text"""
        if self.device(**selector).wait(timeout=timeout):
            self.device(**selector).set_text(text)
            time.sleep(1)
            return True
        return False

    def debug_screen_elements(self):
        """Debug function to print all clickable elements with text"""
        print("=== DEBUG: Current screen elements ===")
        try:
            # Check specific elements that might contain "Create"
            all_elements = self.device(textContains="Create")
            for i in range(all_elements.count):
                elem = all_elements[i]
                if elem.exists:
                    info = elem.info
                    print(f"Element {i}: text='{info.get('text', '')}', clickable={info.get('clickable', False)}")
                    print(f"  className='{info.get('className', '')}', bounds={info.get('bounds', {})}")
        except Exception as e:
            print(f"Debug error: {e}")
        print("=== END DEBUG ===\n")
    
    def create_account(self):
        """Main function to create Gmail account"""
        print("Starting Gmail account creation...")
        
        # Generate account info
        first_name, last_name = self.generate_name()
        password = self.generate_password()
        birthday = self.generate_birthday()
        
        self.account_info = {
            'first_name': first_name,
            'last_name': last_name,
            'password': password,
            'birthday': birthday,
            'created_at': datetime.now().isoformat()
        }
        
        print(f"Generated info: {first_name} {last_name}, Birthday: {birthday}")
        
        try:
            # Step 1: Open Settings
            print("Opening Settings...")
            self.device.app_start("com.android.settings")
            time.sleep(3)
            
            # Step 2: Navigate to Accounts
            print("Looking for Accounts...")
            accounts_selectors = [
                {"text": "Passwords & accounts"},
                {"textContains": "Passwords"},
                {"text": "Passwords & accounts"},
                {"textContains": "& accounts"}
            ]
            
            found_accounts = False
            for selector in accounts_selectors:
                if self.wait_and_click(selector, timeout=5):
                    found_accounts = True
                    break
            
            if not found_accounts:
                print("Scrolling to find Accounts...")
                self.device.swipe(500, 800, 500, 300, 0.5)
                time.sleep(2)
                for selector in accounts_selectors:
                    if self.wait_and_click(selector, timeout=3):
                        found_accounts = True
                        break
            
            if not found_accounts:
                raise Exception("Could not find Accounts section")
            
            time.sleep(2)
            
            # Step 3: Add Google Account
            print("Adding Google account...")
            add_selectors = [
                {"text": "Add account"},
                {"textContains": "Add"},
                {"resourceId": "android:id/button1"}
            ]
            
            for selector in add_selectors:
                if self.wait_and_click(selector, timeout=5):
                    break
            
            time.sleep(2)
            
            # Step 4: Select Google
            print("Selecting Google...")
            if not self.wait_and_click({"text": "Google"}, timeout=10):
                if not self.wait_and_click({"textContains": "Google"}, timeout=5):
                    raise Exception("Could not find Google option")
            
            time.sleep(3)



            if not self.wait_and_click({"text": "Sign in to your Google Account"}, timeout=10):
                raise Exception("Could not find Google option")

            
            # Step 5: Create account
            print("Creating new account...")

            # Debug: Show what's on screen
            self.debug_screen_elements()

            create_selectors = [
                {"text": "Create account"},
                {"textContains": "Create account"},
                {"textMatches": ".*Create account.*"},
                {"className": "android.widget.TextView", "textContains": "Create"},
                {"clickable": True, "textContains": "Create"},
                {"text": "For personal use"}
            ]

            account_created = False
            for i, selector in enumerate(create_selectors):
                print(f"Trying selector {i+1}: {selector}")
                if self.wait_and_click(selector, timeout=3):
                    print(f"Success with selector {i+1}")
                    account_created = True
                    break

            if not account_created:
                # Try clicking by coordinates if text selectors fail
                print("Trying coordinate click for Create account...")
                # Look for any element containing "Create"
                create_elements = self.device(textContains="Create")
                if create_elements.exists:
                    for i in range(create_elements.count):
                        elem = create_elements[i]
                        if elem.exists:
                            print(f"Clicking on element {i} with text: '{elem.info.get('text', '')}'")
                            elem.click()
                            account_created = True
                            break

            if not account_created:
                # Last resort: try clicking on any text that looks like a link
                print("Last resort: looking for any account creation links...")
                link_selectors = [
                    {"textContains": "account"},
                    {"textContains": "Account"},
                    {"textContains": "sign up"},
                    {"textContains": "Sign up"}
                ]

                for selector in link_selectors:
                    if self.wait_and_click(selector, timeout=2):
                        account_created = True
                        break

            if not account_created:
                # Take screenshot for debugging
                debug_screenshot = f"debug_create_account_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                self.device.screenshot(debug_screenshot)
                print(f"Debug screenshot saved: {debug_screenshot}")
                raise Exception("Could not find or click Create account option")
            
            time.sleep(2)
            
            # If "For personal use" appears, click it
            self.wait_and_click({"text": "For personal use"}, timeout=3)
            time.sleep(2)
            
            # Step 6: Fill in first name
            print(f"Entering first name: {first_name}")
            first_name_selectors = [
                {"text": "First name"},
                {"textContains": "First"},
                {"resourceId": "firstName"}
            ]
            
            for selector in first_name_selectors:
                if self.wait_and_type(selector, first_name, timeout=5):
                    break
            
            # Step 7: Fill in last name
            print(f"Entering last name: {last_name}")
            last_name_selectors = [
                {"text": "Last name"},
                {"textContains": "Last"},
                {"resourceId": "lastName"}
            ]
            
            for selector in last_name_selectors:
                if self.wait_and_type(selector, last_name, timeout=5):
                    break
            
            # Step 8: Click Next
            print("Clicking Next...")
            self.wait_and_click({"text": "Next"}, timeout=5)
            time.sleep(3)
            
            # Step 9: Fill birthday and gender (if required)
            print(f"Entering birthday: {birthday}")
            if self.device(textContains="birthday").exists or self.device(textContains="Birthday").exists:
                # Handle birthday input
                month, day, year = birthday.split('/')
                
                # Try different birthday input methods
                if self.device(text="Month").exists:
                    self.device(text="Month").click()
                    time.sleep(1)
                    self.device.send_keys(month)
                
                if self.device(text="Day").exists:
                    self.device(text="Day").click()
                    time.sleep(1)
                    self.device.send_keys(day)
                
                if self.device(text="Year").exists:
                    self.device(text="Year").click()
                    time.sleep(1)
                    self.device.send_keys(year)
                
                time.sleep(2)
                self.wait_and_click({"text": "Next"}, timeout=5)
                time.sleep(3)
            
            # Step 10: Handle username selection
            print("Handling username selection...")
            time.sleep(5)  # Wait for username suggestions to load
            
            # Try to select a suggested username or create custom one
            if self.device(textContains="@gmail.com").exists:
                # Click on first suggested username
                self.device(textContains="@gmail.com").click()
                time.sleep(2)
            
            self.wait_and_click({"text": "Next"}, timeout=5)
            time.sleep(3)
            
            # Step 11: Set password
            print("Setting password...")
            password_selectors = [
                {"text": "Password"},
                {"textContains": "password"},
                {"resourceId": "passwd"}
            ]
            
            for selector in password_selectors:
                if self.wait_and_type(selector, password, timeout=5):
                    break
            
            # Confirm password
            confirm_selectors = [
                {"text": "Confirm"},
                {"textContains": "Confirm"},
                {"resourceId": "confirm-passwd"}
            ]
            
            for selector in confirm_selectors:
                if self.wait_and_type(selector, password, timeout=5):
                    break
            
            self.wait_and_click({"text": "Next"}, timeout=5)
            time.sleep(3)
            
            # Step 12: Accept terms and conditions
            print("Accepting terms...")
            accept_selectors = [
                {"text": "I agree"},
                {"text": "Accept"},
                {"text": "Agree"},
                {"textContains": "agree"}
            ]
            
            for selector in accept_selectors:
                if self.wait_and_click(selector, timeout=5):
                    break
            
            time.sleep(5)
            
            # Step 13: Final screenshot and save info
            print("Taking final screenshot...")
            screenshot_path = f"gmail_account_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.device.screenshot(screenshot_path)
            
            # Save account info
            info_file = f"account_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(info_file, 'w') as f:
                json.dump(self.account_info, f, indent=2)
            
            print(f"Account creation completed!")
            print(f"Screenshot saved: {screenshot_path}")
            print(f"Account info saved: {info_file}")
            print(f"Account details: {self.account_info}")
            
            return True
            
        except Exception as e:
            print(f"Error during account creation: {e}")
            # Take error screenshot
            error_screenshot = f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.device.screenshot(error_screenshot)
            print(f"Error screenshot saved: {error_screenshot}")
            return False

if __name__ == "__main__":
    print("=== Gmail Account Creator ===")
    print("Make sure your Android device is connected and unlocked")
    input("Press Enter to start...")

    creator = GmailAccountCreator()
    success = creator.create_account()

    if success:
        print("\n✅ Account creation completed successfully!")
    else:
        print("\n❌ Account creation failed. Check error screenshot.")
