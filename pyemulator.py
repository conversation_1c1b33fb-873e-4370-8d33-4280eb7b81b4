import subprocess
import time
import os
from appium import webdriver
from appium.options.android import UiAutomator2Options

class AndroidEmulatorController:
    def __init__(self, avd_name="Android12_PlayStore"):
        self.avd_name = avd_name
        self.emulator_process = None
        self.driver = None
        
    def start_emulator(self):
        """Launch the Android emulator with Play Store"""
        try:
            cmd = [
                "emulator",
                "-avd", self.avd_name,
                "-no-audio",
                "-gpu", "swiftshader_indirect",
                "-no-metrics"
            ]
            
            print(f"Starting emulator with Play Store: {self.avd_name}")
            self.emulator_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for emulator to boot
            print("Waiting for emulator to boot...")
            self._wait_for_device()
            print("Emulator with Play Store is ready!")
            
        except Exception as e:
            print(f"Error starting emulator: {e}")
            return False

        return True
    
    def _wait_for_device(self):
        """Wait for the emulator to be ready"""
        timeout = 180  # 3 minutes (reduced from 5)
        start_time = time.time()
        device_detected = False

        while time.time() - start_time < timeout:
            try:
                # First, check if ADB can see any devices
                devices_result = subprocess.run(
                    ["adb", "devices"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if devices_result.returncode == 0:
                    devices_output = devices_result.stdout
                    if "emulator" in devices_output and "device" in devices_output:
                        if not device_detected:
                            print("Emulator detected by ADB, checking boot status...")
                            device_detected = True

                        # Now check if boot is completed
                        boot_result = subprocess.run(
                            ["adb", "shell", "getprop", "sys.boot_completed"],
                            capture_output=True,
                            text=True,
                            timeout=10
                        )

                        if boot_result.returncode == 0 and boot_result.stdout.strip() == "1":
                            print("System booted successfully!")

                            # Quick check if UI is responsive
                            ui_result = subprocess.run(
                                ["adb", "shell", "dumpsys", "window", "displays"],
                                capture_output=True,
                                text=True,
                                timeout=10
                            )

                            if ui_result.returncode == 0:
                                print("UI system is ready!")
                                time.sleep(10)  # Reduced wait time
                                return True

            except subprocess.TimeoutExpired:
                print("ADB command timed out, retrying...")
            except Exception as e:
                print(f"Error checking device status: {e}")

            if not device_detected:
                print("Waiting for emulator to appear in ADB...")
            else:
                print("Waiting for boot to complete...")
            time.sleep(3)  # Reduced from 5 seconds

        # Don't raise error, just return False and let user decide
        print("⚠️  Emulator may still be starting. You can:")
        print("   1. Wait a bit more and try connecting manually")
        print("   2. Cancel and restart if needed")
        return False
    
    def connect_appium(self, appium_port=4723):
        """Connect to the emulator via Appium"""
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator"
        options.automation_name = "UiAutomator2"
        
        try:
            self.driver = webdriver.Remote(
                f"http://localhost:{appium_port}",
                options=options
            )
            print("Connected to Appium successfully!")
            return True
            
        except Exception as e:
            print(f"Error connecting to Appium: {e}")
            return False
    
    def test_play_store(self):
        """Test accessing Play Store"""
        if not self.driver:
            print("Driver not initialized")
            return
        
        try:
            # Launch Play Store
            self.driver.start_activity("com.android.vending", ".AssetBrowserActivity")
            time.sleep(5)
            
            print("Play Store launched successfully!")
            
            # Take screenshot
            self.driver.save_screenshot("play_store_screenshot.png")
            print("Screenshot of Play Store saved!")
            
        except Exception as e:
            print(f"Error accessing Play Store: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            
        if self.emulator_process:
            self.emulator_process.terminate()
            self.emulator_process.wait()

# Usage
if __name__ == "__main__":
    controller = AndroidEmulatorController("Android12_PlayStore")

    try:
        print("🚀 Starting Android emulator...")
        controller.start_emulator()

        print("\n📱 Emulator should be starting up now!")
        print("💡 If you see the emulator window, it's working correctly.")
        print("⏱️  The emulator may take 1-2 minutes to fully load.")

        choice = input("\nDo you want to test with Appium? (y/n): ").lower().strip()

        if choice == 'y':
            print("\n🔧 Make sure Appium server is running:")
            print("   Command: appium --port 4723")
            input("Press Enter after starting Appium server...")

            if controller.connect_appium():
                controller.test_play_store()
                input("Press Enter to cleanup...")
            else:
                print("❌ Could not connect to Appium")
        else:
            print("✅ Emulator started! You can now use it manually.")
            input("Press Enter when done to cleanup...")

    except KeyboardInterrupt:
        print("\n🛑 Process interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        print("🧹 Cleaning up...")
        controller.cleanup()
        print("✅ Done!")