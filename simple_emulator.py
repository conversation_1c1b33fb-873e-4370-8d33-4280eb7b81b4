import subprocess
import time
import os
import uiautomator2 as u2

class SimpleEmulatorLauncher:
    def __init__(self, avd_name="Android12_PlayStore"):
        self.avd_name = avd_name
        self.emulator_process = None
        
    def start_emulator(self):
        """Launch the Android emulator"""
        try:
            cmd = [
                "emulator",
                "-avd", self.avd_name,
                "-no-audio",
                "-gpu", "swiftshader_indirect",
                "-no-metrics",
                "-no-boot-anim"  # Skip boot animation for faster startup
            ]
            
            print(f"🚀 Starting emulator: {self.avd_name}")
            self.emulator_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,  # Hide emulator output
                stderr=subprocess.DEVNULL
            )
            
            print("📱 Emulator is starting...")
            print("⏱️  This may take 1-2 minutes...")
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting emulator: {e}")
            return False
    
    def wait_for_device(self, timeout=180):
        """Wait for emulator to be ready for uiautomator2"""
        print("🔍 Waiting for device to be ready...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check if ADB can see the device
                result = subprocess.run(
                    ["adb", "devices"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                if "emulator" in result.stdout and "device" in result.stdout:
                    print("📱 Device detected by ADB")
                    
                    # Try to connect with uiautomator2
                    try:
                        d = u2.connect()
                        device_info = d.info
                        print(f"✅ Connected! Device: {device_info.get('productName', 'Unknown')}")
                        print(f"📊 Screen: {device_info.get('displayWidth', '?')}x{device_info.get('displayHeight', '?')}")
                        return True
                        
                    except Exception as e:
                        print(f"🔄 uiautomator2 not ready yet: {str(e)[:50]}...")
                        
            except subprocess.TimeoutExpired:
                print("⏳ ADB command timed out, retrying...")
            except Exception as e:
                print(f"🔄 Checking device: {str(e)[:50]}...")
            
            time.sleep(5)
        
        print("⚠️  Device detection timed out")
        return False
    
    def test_connection(self):
        """Test uiautomator2 connection and take screenshot"""
        try:
            print("🧪 Testing uiautomator2 connection...")
            d = u2.connect()
            
            # Take a screenshot to verify everything works
            screenshot_path = f"emulator_test_{int(time.time())}.png"
            d.screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
            
            # Get device info
            info = d.info
            print(f"📱 Device Model: {info.get('productName', 'Unknown')}")
            print(f"🤖 Android Version: {info.get('version', 'Unknown')}")
            print(f"📊 Screen Size: {info.get('displayWidth', '?')}x{info.get('displayHeight', '?')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False
    
    def cleanup(self):
        """Stop the emulator"""
        if self.emulator_process:
            print("🛑 Stopping emulator...")
            self.emulator_process.terminate()
            try:
                self.emulator_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print("🔨 Force killing emulator...")
                self.emulator_process.kill()
            print("✅ Emulator stopped")

def main():
    launcher = SimpleEmulatorLauncher("Android12_PlayStore")
    
    try:
        print("=== Simple Android Emulator Launcher ===")
        print("This will start your emulator for use with uiautomator2\n")
        
        # Start emulator
        if not launcher.start_emulator():
            return
        
        # Wait for device
        if launcher.wait_for_device():
            print("\n🎉 Emulator is ready!")
            
            # Test connection
            if launcher.test_connection():
                print("\n✅ Everything is working perfectly!")
                print("🚀 You can now run your Gmail account creator:")
                print("   python gmail_account_creator.py")
                
                choice = input("\nRun Gmail account creator now? (y/n): ").lower().strip()
                if choice == 'y':
                    print("\n🤖 Starting Gmail account creator...")
                    try:
                        subprocess.run(["python", "gmail_account_creator.py"], check=True)
                    except subprocess.CalledProcessError as e:
                        print(f"❌ Gmail creator failed: {e}")
                    except KeyboardInterrupt:
                        print("\n🛑 Gmail creator interrupted")
                else:
                    input("\nPress Enter when you're done using the emulator...")
            else:
                print("❌ Connection test failed, but emulator might still work")
                input("Press Enter to cleanup...")
        else:
            print("⚠️  Could not detect device automatically")
            print("💡 The emulator might still be starting up")
            print("🔍 Check if you can see the emulator window")
            
            choice = input("Continue anyway? (y/n): ").lower().strip()
            if choice == 'y':
                input("Press Enter when done...")
    
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        launcher.cleanup()

if __name__ == "__main__":
    main()
